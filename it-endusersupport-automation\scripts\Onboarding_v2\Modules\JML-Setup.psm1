#Requires -Version 5.1

<#
.SYNOPSIS
JML Setup and Environment Validation Module - Minimal Version

.DESCRIPTION
This is a minimal version of the setup module to test basic functionality.

.NOTES
Version:        1.12
Author:         <PERSON>
Creation Date:  2025-01-09
#>

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Logging.psm1") -Force

# Module variables
$script:Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Information = "Cyan"
    Debug = "Gray"
    Progress = "Blue"
}

function Write-SetupMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('Success', 'Warning', 'Error', 'Information', 'Debug', 'Progress')]
        [string]$Type = 'Information',

        [Parameter(Mandatory = $false)]
        [hashtable]$AuditTrail
    )

    try {
        $logLevel = switch ($Type) {
            'Success' { 'INFO' }
            'Warning' { 'WARNING' }
            'Error' { 'ERROR' }
            'Information' { 'INFO' }
            'Debug' { 'DEBUG' }
            'Progress' { 'INFO' }
            default { 'INFO' }
        }

        Write-Host "[$Type] $Message" -ForegroundColor $script:Colors[$Type]

        if (Get-Command Write-SecureLog -ErrorAction SilentlyContinue) {
            Write-SecureLog -Message "Setup: $Message" -LogLevel $logLevel -AuditTrail $AuditTrail
        }
    }
    catch {
        Write-Host "[$Type] $Message" -ForegroundColor $script:Colors[$Type]
        Write-Warning "Setup logging failed: $($_.Exception.Message)"
    }
}

function Test-ModuleAvailability {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ModuleName
    )

    try {
        $module = Get-Module -ListAvailable -Name $ModuleName -ErrorAction SilentlyContinue
        $isAvailable = $null -ne $module
        return $isAvailable
    }
    catch {
        Write-SetupMessage "Error checking module availability for $ModuleName : $($_.Exception.Message)" -Type Error
        return $false
    }
}

function Install-RequiredModule {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ModuleName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Description,

        [Parameter(Mandatory = $false)]
        [ValidateSet('CurrentUser', 'AllUsers')]
        [string]$Scope = 'CurrentUser'
    )

    try {
        if (Test-ModuleAvailability -ModuleName $ModuleName) {
            Write-SetupMessage "Module $ModuleName is already installed." -Type Success
            return $true
        }

        Write-SetupMessage "Installing module: $ModuleName ($Description)" -Type Progress

        $installParams = @{
            Name = $ModuleName
            Scope = $Scope
            Force = $true
            SkipPublisherCheck = $true
            AllowClobber = $true
            ErrorAction = 'Stop'
        }

        Install-Module @installParams
        Write-SetupMessage "Successfully installed $ModuleName" -Type Success
        return $true
    }
    catch {
        $errorMsg = "Failed to install $ModuleName : $($_.Exception.Message)"
        Write-SetupMessage $errorMsg -Type Error
        return $false
    }
}

function Test-Environment {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        Write-SetupMessage "Validating environment..." -Type Progress
        
        $issues = @()
        $warnings = @()

        if ($PSVersionTable.PSVersion.Major -lt 5) {
            $issues += "PowerShell 5.1 or higher is required. Current version: $($PSVersionTable.PSVersion)"
        } else {
            Write-SetupMessage "PowerShell version: $($PSVersionTable.PSVersion) ✓" -Type Success
        }

        $requiredModules = @(
            @{Name = "ActiveDirectory"; Description = "Active Directory management"; Required = $true }
        )

        foreach ($module in $requiredModules) {
            if (Test-ModuleAvailability -ModuleName $module.Name) {
                Write-SetupMessage "Module $($module.Name): Available ✓" -Type Success
            } else {
                if ($module.Required) {
                    $issues += "Required module missing: $($module.Name) ($($module.Description))"
                } else {
                    $warnings += "Optional module missing: $($module.Name) ($($module.Description))"
                }
            }
        }

        foreach ($warning in $warnings) {
            Write-SetupMessage "  - $warning" -Type Warning
        }

        if ($issues.Count -eq 0) {
            Write-SetupMessage "Environment validation completed successfully! ✓" -Type Success
            return $true
        } else {
            Write-SetupMessage "Environment validation found issues:" -Type Error
            foreach ($issue in $issues) {
                Write-SetupMessage "  - $issue" -Type Error
            }
            return $false
        }
    }
    catch {
        Write-SetupMessage "Environment validation failed: $($_.Exception.Message)" -Type Error
        return $false
    }
}

function Start-JMLSetup {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [switch]$SetupCredentials,

        [Parameter(Mandatory = $false)]
        [switch]$ValidateEnvironment,

        [Parameter(Mandatory = $false)]
        [switch]$InstallMissingModules
    )

    try {
        Write-SetupMessage "JML Admin Account Script Setup Utility" -Type Progress
        Write-SetupMessage "Version 1.12 - Enhanced Security Edition" -Type Information

        $setupSuccess = $true

        if ($ValidateEnvironment) {
            Write-SetupMessage "Starting environment validation..." -Type Progress
            $envValid = Test-Environment

            if (-not $envValid) {
                Write-SetupMessage "Environment validation failed." -Type Warning
                $setupSuccess = $false
            } else {
                Write-SetupMessage "Environment validation passed successfully!" -Type Success
            }
        }

        if ($setupSuccess) {
            Write-SetupMessage "Setup completed successfully! ✓" -Type Success
        } else {
            Write-SetupMessage "Setup completed with issues." -Type Warning
        }

        return $setupSuccess
    }
    catch {
        Write-SetupMessage "Setup failed with error: $($_.Exception.Message)" -Type Error
        return $false
    }
}

function Get-JMLVersion {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()

    return @{
        JMLVersion = "1.12"
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        ComputerName = $env:COMPUTERNAME
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
}

Export-ModuleMember -Function Write-SetupMessage, Test-ModuleAvailability, Install-RequiredModule, Test-Environment, Start-JMLSetup, Get-JMLVersion
