# Minimal test script to isolate the hanging issue
param(
    [switch]$SkipJiraIntegration
)

Write-Host "=== JML Minimal Test ===" -ForegroundColor Cyan
Write-Host "Testing basic functionality..." -ForegroundColor Yellow

# Test 1: Script directory detection
$ScriptDirectory = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script Directory: $ScriptDirectory" -ForegroundColor Green

# Test 2: Modules directory
$ModulesDirectory = Join-Path $ScriptDirectory "Modules"
Write-Host "Modules Directory: $ModulesDirectory" -ForegroundColor Green
Write-Host "Modules Directory Exists: $(Test-Path $ModulesDirectory)" -ForegroundColor Green

# Test 3: Configuration file
$ConfigPath = Join-Path $ScriptDirectory "AdminAccountConfig.psd1"
Write-Host "Config Path: $ConfigPath" -ForegroundColor Green
Write-Host "Config File Exists: $(Test-Path $ConfigPath)" -ForegroundColor Green

# Test 4: Module loading
Write-Host "Testing module loading..." -ForegroundColor Yellow
try {
    Import-Module (Join-Path $ModulesDirectory "JML-Configuration.psm1") -Force
    Write-Host "JML-Configuration loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to load JML-Configuration: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Configuration initialization
Write-Host "Testing configuration initialization..." -ForegroundColor Yellow
try {
    $configResult = Initialize-SmartConfiguration -ConfigPath $ConfigPath
    Write-Host "Configuration initialized: $configResult" -ForegroundColor Green
} catch {
    Write-Host "Configuration failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Interactive session detection
Write-Host "Testing interactive session detection..." -ForegroundColor Yellow
try {
    $isInteractive = Test-InteractiveSession
    Write-Host "Interactive session: $isInteractive" -ForegroundColor Green
} catch {
    Write-Host "Interactive session test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== Test Complete ===" -ForegroundColor Cyan
