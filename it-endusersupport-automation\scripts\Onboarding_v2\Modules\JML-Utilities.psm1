#Requires -Version 5.1

<#
.SYNOPSIS
JML Utilities Module

.DESCRIPTION
This module provides utility functions for the JML (<PERSON><PERSON>, Mover, Leaver) 
admin account management script. It includes password generation, UPN construction,
user name retrieval, and other general-purpose functions.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- System.Web assembly for password generation
- ActiveDirectory module for user operations
#>

# Import required assemblies
Add-Type -AssemblyName System.Web

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Logging.psm1") -Force

<#
.SYNOPSIS
Generates a secure password with configurable complexity requirements.

.DESCRIPTION
Creates cryptographically secure passwords using System.Web.Security.Membership
with configurable length and special character requirements based on configuration.

.PARAMETER Length
The length of the password to generate.

.PARAMETER MinSpecialChars
Minimum number of special characters to include.

.EXAMPLE
$password = New-SecurePassword -Length 16 -MinSpecialChars 3

.NOTES
Uses configuration settings if available, otherwise uses provided parameters or defaults.
#>
function New-SecurePassword {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidateRange(8, 128)]
        [int]$Length = 12,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MinSpecialChars = 2
    )

    try {
        # Get configuration
        $config = Get-ModuleConfiguration

        # Use configuration if available
        if ($config) {
            $Length = $config.ActiveDirectory.PasswordPolicy.MinLength
            $MinSpecialChars = $config.ActiveDirectory.PasswordPolicy.MinSpecialChars
        }

        $password = [System.Web.Security.Membership]::GeneratePassword($Length, $MinSpecialChars)

        Write-SecureLog -Message "Generated secure password with length $Length and $MinSpecialChars special characters" -LogLevel "INFO" -AuditTrail @{
            Operation = "PasswordGeneration"
            PasswordLength = $Length
            SpecialChars = $MinSpecialChars
        }

        return $password
    }
    catch {
        Write-SecureLog -Message "Failed to generate password: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
}

<#
.SYNOPSIS
Constructs a standard User Principal Name from first and last names.

.DESCRIPTION
Creates a UPN following organizational naming conventions with proper sanitization
and validation. Handles special characters and ensures compliance with UPN format requirements.

.PARAMETER FirstName
The user's first name.

.PARAMETER LastName
The user's last name.

.PARAMETER Domain
The domain to append to the UPN.

.EXAMPLE
$upn = New-StandardUPN -FirstName "John" -LastName "Doe" -Domain "domain.com"
Returns: "<EMAIL>"

.NOTES
Implements proper sanitization and validation for enterprise environments.
#>
function New-StandardUPN {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$FirstName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LastName,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')]
        [string]$Domain
    )

    try {
        # Sanitize input
        $cleanFirstName = $FirstName.Trim() -replace '[^a-zA-Z]', ''
        $cleanLastName = $LastName.Trim() -replace '[^a-zA-Z]', ''

        if ([string]::IsNullOrEmpty($cleanFirstName) -or [string]::IsNullOrEmpty($cleanLastName)) {
            throw "Invalid first name or last name after sanitization"
        }

        # Simple sanitization and formatting: lowercase, replace spaces with dots, remove other invalid chars, clean up dots
        $firstNameClean = $cleanFirstName -replace '\s+', '.' -replace '[^\w.-]', ''
        $lastNameClean = $cleanLastName -replace '\s+', '.' -replace '[^\w.-]', ''
        $upn = "$($firstNameClean.ToLower()).$($lastNameClean.ToLower())@$Domain"
        $upn = $upn -replace '\.\.', '.' # Replace double dots
        $upn = $upn.Trim('.') # Remove leading/trailing dots

        Write-SecureLog -Message "Constructed UPN for user" -LogLevel "INFO" -AuditTrail @{
            Operation = "UPNConstruction"
            Domain = $Domain
            UPNHash = Get-StringHash -InputString $upn
        }

        return $upn
    }
    catch {
        Write-SecureLog -Message "Failed to construct UPN: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
}

<#
.SYNOPSIS
Retrieves the full name of the current user from Active Directory.

.DESCRIPTION
Gets the display name of the currently executing user by querying Active Directory
with proper error handling and fallback mechanisms.

.OUTPUTS
String containing the user's full name, or "Unknown User" if retrieval fails.

.EXAMPLE
$userName = Get-CurrentUserName

.NOTES
Implements secure user identity retrieval with comprehensive error handling.
#>
function Get-CurrentUserName {
    [CmdletBinding()]
    [OutputType([string])]
    param()

    try {
        # Use [System.Security.Principal.WindowsIdentity]::GetCurrent().Name for domain\username
        $currentUserSamAccountName = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name.Split('\')[1]

        $currentUser = Get-ADUser -Identity $currentUserSamAccountName -Properties GivenName, Surname -ErrorAction Stop
        return "$($currentUser.GivenName) $($currentUser.Surname)"
    } catch {
        Write-SecureLog -Message "Error retrieving current user name: $($_.Exception.Message)" -LogLevel "ERROR"
        return "Unknown User"
    }
}

<#
.SYNOPSIS
Ensures required modules are installed and imported with enhanced error handling.

.DESCRIPTION
Validates and installs required PowerShell modules using approved security practices.
Implements retry logic and comprehensive error handling.

.PARAMETER ModuleName
Name of the module to ensure is available.

.PARAMETER RequiredVersion
Optional specific version requirement.

.EXAMPLE
Confirm-RequiredModule -ModuleName "ActiveDirectory"

.NOTES
Security: Uses CurrentUser scope to avoid requiring elevated privileges.
#>
function Confirm-RequiredModule {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ModuleName,

        [Parameter(Mandatory = $false)]
        [string]$RequiredVersion
    )

    try {
        Write-Progress -Activity "Initializing Script" -Status "Checking module: $ModuleName" -PercentComplete 30

        $installedModule = Get-Module -ListAvailable -Name $ModuleName -ErrorAction SilentlyContinue

        if (-not $installedModule) {
            Write-Host "Module $ModuleName not found. Installing for current user..." -ForegroundColor Yellow

            try {
                $installParams = @{
                    Name = $ModuleName
                    Scope = 'CurrentUser'
                    Force = $true
                    SkipPublisherCheck = $true
                    AllowClobber = $true
                    ErrorAction = 'Stop'
                }

                if ($RequiredVersion) {
                    $installParams.RequiredVersion = $RequiredVersion
                }

                Install-Module @installParams
                Write-Host "Module $ModuleName installed successfully." -ForegroundColor Green
            }
            catch [System.UnauthorizedAccessException] {
                throw "Insufficient permissions to install module $ModuleName. Run as administrator or install manually."
            }
            catch [System.Net.WebException] {
                throw "Network error installing module $ModuleName. Check internet connectivity and proxy settings."
            }
            catch {
                throw "Failed to install module ${ModuleName}: $($_.Exception.Message)"
            }
        }
        else {
            Write-Host "Module $ModuleName is available." -ForegroundColor Green
        }

        # Import the module
        Import-Module $ModuleName -ErrorAction Stop -Force
        Write-Host "Module $ModuleName imported successfully." -ForegroundColor Green

    }
    catch [System.IO.FileNotFoundException] {
        throw "Module $ModuleName files not found after installation. Manual installation may be required."
    }
    catch [System.Management.Automation.PSInvalidOperationException] {
        throw "Failed to import module $ModuleName. The module may be corrupted or incompatible."
    }
    catch {
        throw "Error with module ${ModuleName}: $($_.Exception.Message)"
    }
}

<#
.SYNOPSIS
Validates input strings against security patterns and sanitizes them.

.DESCRIPTION
Performs comprehensive input validation and sanitization based on configuration
settings to prevent injection attacks and ensure data integrity.

.PARAMETER InputString
The string to validate and sanitize.

.PARAMETER InputType
The type of input (UPN, SamAccountName, DisplayName, etc.).

.EXAMPLE
$cleanInput = Confirm-InputSecurity -InputString $userInput -InputType "UPN"

.NOTES
Implements enterprise-grade input validation and sanitization.
#>
function Confirm-InputSecurity {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [AllowEmptyString()]
        [string]$InputString,

        [Parameter(Mandatory = $true)]
        [ValidateSet('UPN', 'SamAccountName', 'DisplayName', 'General')]
        [string]$InputType
    )

    try {
        # Get configuration
        $config = Get-ModuleConfiguration

        if ([string]::IsNullOrEmpty($InputString)) {
            return $InputString
        }

        $sanitizedInput = $InputString

        # Apply sanitization if enabled
        if ($config -and $config.Security.InputValidation.EnableSanitization) {
            # Remove HTML tags if enabled
            if ($config.Security.InputValidation.RemoveHtmlTags) {
                $sanitizedInput = $sanitizedInput -replace '<[^>]*>', ''
            }

            # Remove script tags if enabled
            if ($config.Security.InputValidation.RemoveScriptTags) {
                $sanitizedInput = $sanitizedInput -replace '<script[^>]*>.*?</script>', ''
            }

            # Trim whitespace
            $sanitizedInput = $sanitizedInput.Trim()
        }

        # Validate against patterns if strict validation is enabled
        if ($config -and $config.Security.InputValidation.EnableStrictValidation) {
            $pattern = switch ($InputType) {
                'UPN' { $config.Security.InputValidation.AllowedPatterns.UPN }
                'SamAccountName' { $config.Security.InputValidation.AllowedPatterns.SamAccountName }
                'DisplayName' { $config.Security.InputValidation.AllowedPatterns.DisplayName }
                default { $null }
            }

            if ($pattern -and $sanitizedInput -notmatch $pattern) {
                throw "Input validation failed for $InputType. Input does not match required pattern."
            }

            # Check maximum length
            if ($sanitizedInput.Length -gt $config.Security.InputValidation.MaxInputLength) {
                throw "Input validation failed for $InputType. Input exceeds maximum length of $($config.Security.InputValidation.MaxInputLength) characters."
            }
        }

        return $sanitizedInput
    }
    catch {
        Write-SecureLog -Message "Input validation failed: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
}

<#
.SYNOPSIS
Validates and prompts for a User Principal Name (UPN).

.DESCRIPTION
Prompts the user to enter a UPN and validates it against the configured domain.
Ensures the UPN follows the correct format and belongs to the expected domain.

.PARAMETER PromptMessage
The message to display when prompting for the UPN.

.PARAMETER AllowEmpty
Whether to allow empty input (optional UPN).

.OUTPUTS
String containing the validated UPN.

.EXAMPLE
$upn = Get-ValidatedUPN -PromptMessage "Enter user UPN"

.EXAMPLE
$upn = Get-ValidatedUPN -PromptMessage "Enter UPN (optional)" -AllowEmpty

.NOTES
Validates UPN format and domain membership.
#>
function Get-ValidatedUPN {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $false)]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset,

        [Parameter(Mandatory = $false)]
        [string]$PromptMessage,

        [Parameter(Mandatory = $false)]
        [switch]$AllowEmpty
    )

    try {
        # Get configuration for domain validation
        $config = Get-ModuleConfiguration
        $expectedDomain = if ($Domain) { $Domain } else { $config.ActiveDirectory.Domain }

        # Set appropriate prompt message based on context
        if (-not $PromptMessage) {
            if ($ForDeletion) {
                $PromptMessage = "Enter the UPN of the user whose admin account should be deleted"
            } elseif ($ForReset) {
                $PromptMessage = "Enter the UPN of the user whose admin account password should be reset"
            } else {
                $PromptMessage = "Enter User Principal Name (UPN)"
            }
        }

        do {
            $upn = Read-Host $PromptMessage

            # Handle empty input
            if ([string]::IsNullOrWhiteSpace($upn)) {
                if ($AllowEmpty) {
                    return ""
                } else {
                    Write-Host "UPN cannot be empty. Please enter a valid UPN." -ForegroundColor Yellow
                    continue
                }
            }

            # Validate UPN format
            if ($upn -notmatch '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$') {
                Write-Host "Invalid UPN format. Please enter a valid email-like UPN (e.g., <EMAIL>)." -ForegroundColor Yellow
                continue
            }

            # Extract domain from UPN
            $upnDomain = $upn.Split('@')[1]

            # Validate domain
            if ($upnDomain -ne $expectedDomain) {
                Write-Host "UPN domain '$upnDomain' does not match expected domain '$expectedDomain'." -ForegroundColor Yellow
                $continue = Read-Host "Do you want to continue anyway? (y/N)"
                if ($continue -notmatch '^[Yy]') {
                    continue
                }
            }

            # Validate input security
            try {
                $validatedUpn = Confirm-InputSecurity -InputValue $upn -InputType "UPN"
                return $validatedUpn
            }
            catch {
                Write-Host "UPN validation failed: $($_.Exception.Message)" -ForegroundColor Red
                continue
            }

        } while ($true)
    }
    catch {
        Write-SecureLog -Message "UPN validation failed: $($_.Exception.Message)" -LogLevel "ERROR"
        throw "Failed to get validated UPN: $($_.Exception.Message)"
    }
}

# Export functions
Export-ModuleMember -Function New-SecurePassword, New-StandardUPN, Get-CurrentUserName, Confirm-RequiredModule, Confirm-InputSecurity, Get-ValidatedUPN
