#Requires -Version 5.1
# Note: ActiveDirectory module is required for full functionality but will be checked at runtime

<#
.SYNOPSIS
JML (<PERSON><PERSON>, Mover, Leaver) Admin Account Management System v1.12

.DESCRIPTION
This is the main entry point for the JML Admin Account Management System. It provides
a comprehensive, modular solution for creating, deleting, and resetting admin accounts
in Active Directory with full Jira integration, email notifications, and enterprise-grade
security features.

Key Features:
- Modular architecture with 8 specialized modules
- Zero-configuration deployment with intelligent defaults
- Comprehensive security with data redaction and secure credential storage
- Full Jira integration with ticket validation and automated updates
- Email notifications with retry logic and SSL fallback
- Comprehensive audit trails and logging
- Enterprise-grade error handling and recovery

.PARAMETER ConfigPath
Path to the configuration file. Defaults to 'AdminAccountConfig.psd1' in script directory.

.PARAMETER LogLevel
Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL. Defaults to INFO.

.PARAMETER SkipJiraIntegration
Switch to skip Jira integration for testing or offline scenarios.

.PARAMETER ShowVersion
Switch to display version information and exit.

.PARAMETER RunSetup
Switch to run the setup utility for initial configuration.

.EXAMPLE
.\JML_v1.12.ps1
Runs the script with default settings and interactive menu.

.EXAMPLE
.\JML_v1.12.ps1 -LogLevel DEBUG -SkipJiraIntegration
Runs with debug logging and no Jira integration.

.EXAMPLE
.\JML_v1.12.ps1 -RunSetup
Runs the setup utility for initial configuration.

.EXAMPLE
.\JML_v1.12.ps1 -ShowVersion
Displays version information and exits.

.NOTES
Version:        1.12
Author:         Emmanuel Akinjomo
Creation Date:  2025-01-09
Last Modified:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Required Permissions:
- Active Directory: User creation rights in target OUs
- SMTP: Send email permissions
- File System: Read/Write access to log directory
- Jira: API access with comment and attachment permissions

Dependencies:
- PowerShell 5.1 or higher
- ActiveDirectory module
- Microsoft.PowerShell.SecretManagement module (recommended)
- Microsoft.PowerShell.SecretStore module (recommended)

Module Architecture:
- JML-Configuration.psm1: Configuration management and intelligent defaults
- JML-Security.psm1: Data redaction, credential management, hashing
- JML-Logging.psm1: Secure logging with audit trails
- JML-Utilities.psm1: General utility functions
- JML-ActiveDirectory.psm1: AD operations and user management
- JML-Email.psm1: Email notifications and SMTP operations
- JML-Jira.psm1: Jira integration and API operations
- JML-Setup.psm1: Setup and environment validation
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidateScript({
        if (-not (Test-Path $_ -PathType Leaf)) {
            throw "Configuration file not found: $_"
        }
        if (-not ($_ -match '\.psd1$')) {
            throw "Configuration file must be a PowerShell Data file (.psd1)"
        }
        return $true
    })]
    [string]$ConfigPath,

    [Parameter(Mandatory = $false)]
    [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [switch]$SkipJiraIntegration,

    [Parameter(Mandatory = $false)]
    [switch]$ShowVersion,

    [Parameter(Mandatory = $false)]
    [switch]$RunSetup
)

# Determine script directory robustly
Write-Host "[DEBUG] Starting JML script execution..." -ForegroundColor Magenta
$ScriptDirectory = if ($PSScriptRoot) {
    $PSScriptRoot
} elseif ($MyInvocation.MyCommand.Path) {
    Split-Path -Parent $MyInvocation.MyCommand.Path
} elseif ($script:MyInvocation.MyCommand.Path) {
    Split-Path -Parent $script:MyInvocation.MyCommand.Path
} else {
    # Fallback to current directory
    Get-Location | Select-Object -ExpandProperty Path
}
Write-Host "[DEBUG] Script directory: $ScriptDirectory" -ForegroundColor Magenta

# Set default ConfigPath if not provided
if (-not $ConfigPath) {
    $ConfigPath = Join-Path $ScriptDirectory "AdminAccountConfig.psd1"
}

#region Module Imports and Initialization

# Script constants
$script:JMLVersion = "1.12"
$script:JMLBuildDate = "2025-01-09"

# Import required modules in dependency order with robust error handling
function Import-JMLModules {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ModulesDirectory
    )

    # Validate modules directory exists
    if (-not (Test-Path $ModulesDirectory -PathType Container)) {
        throw "Modules directory not found: $ModulesDirectory"
    }

    # Define modules in dependency order with criticality flags
    $modulesToLoad = @(
        @{Name = "JML-Configuration.psm1"; Critical = $true; Description = "Core configuration management" },
        @{Name = "JML-Security.psm1"; Critical = $true; Description = "Security and credential management" },
        @{Name = "JML-Logging.psm1"; Critical = $true; Description = "Secure logging functionality" },
        @{Name = "JML-Utilities.psm1"; Critical = $true; Description = "Core utility functions" },
        # @{Name = "JML-Setup.psm1"; Critical = $false; Description = "Setup and validation utilities" },
        @{Name = "JML-ActiveDirectory.psm1"; Critical = $false; Description = "Active Directory operations" },
        @{Name = "JML-Email.psm1"; Critical = $false; Description = "Email notification functionality" },
        @{Name = "JML-Jira.psm1"; Critical = $false; Description = "Jira integration" }
    )

    $loadedModules = @()
    $failedModules = @()

    Write-Host "Loading JML modules..." -ForegroundColor Cyan
    Write-Host "Modules Directory: $ModulesDirectory" -ForegroundColor Gray

    foreach ($moduleInfo in $modulesToLoad) {
        $moduleName = $moduleInfo.Name
        $isCritical = $moduleInfo.Critical
        $description = $moduleInfo.Description

        # Always construct the full path fresh to avoid path corruption
        $moduleFullPath = Join-Path $ModulesDirectory $moduleName

        $criticalityText = if ($isCritical) { "[CRITICAL]" } else { "[OPTIONAL]" }
        Write-Host "  Loading: $moduleName $criticalityText" -ForegroundColor Yellow
        Write-Host "    Description: $description" -ForegroundColor Gray
        Write-Host "    Path: $moduleFullPath" -ForegroundColor Gray

        # Validate module file exists
        if (-not (Test-Path $moduleFullPath -PathType Leaf)) {
            $errorMsg = "Module file not found: $moduleFullPath"
            Write-Host "    [ERROR] $errorMsg" -ForegroundColor Red
            $failedModules += @{
                Name = $moduleName
                Path = $moduleFullPath
                Error = $errorMsg
                ErrorType = "FileNotFound"
                Critical = $isCritical
                Description = $description
            }

            if ($isCritical) {
                Write-Host "    [CRITICAL] This is a required module - script cannot continue" -ForegroundColor Red
            }
            continue
        }

        try {
            # Remove module if already loaded to ensure clean import
            $moduleBaseName = $moduleName.Replace('.psm1', '')
            if (Get-Module -Name $moduleBaseName -ErrorAction SilentlyContinue) {
                Remove-Module -Name $moduleBaseName -Force -ErrorAction SilentlyContinue
                Write-Host "    Removed existing module instance" -ForegroundColor Gray
            }

            # Import the module with explicit path and global scope
            Write-Host "    Attempting to import module..." -ForegroundColor Gray
            Import-Module $moduleFullPath -Force -ErrorAction Stop -Verbose:$false -Global

            # Verify the module was actually loaded
            $loadedModule = Get-Module -Name $moduleBaseName -ErrorAction SilentlyContinue
            if ($loadedModule) {
                Write-Host "    [SUCCESS] Module loaded successfully" -ForegroundColor Green
                Write-Host "    Exported functions: $($loadedModule.ExportedFunctions.Count)" -ForegroundColor Gray
                if ($loadedModule.ExportedFunctions.Count -gt 0) {
                    $functionNames = $loadedModule.ExportedFunctions.Keys -join ", "
                    Write-Host "    Functions: $functionNames" -ForegroundColor Gray
                }
                $loadedModules += @{
                    Name = $moduleName
                    Path = $moduleFullPath
                    ModuleInfo = $loadedModule
                    Critical = $isCritical
                    Description = $description
                }
            } else {
                throw "Module imported but not found in loaded modules list"
            }
        }
        catch {
            $errorMsg = "Failed to import module: $($_.Exception.Message)"
            Write-Host "    [ERROR] $errorMsg" -ForegroundColor Red
            Write-Host "    Exception Type: $($_.Exception.GetType().Name)" -ForegroundColor Red

            if ($isCritical) {
                Write-Host "    [CRITICAL] This is a required module - script cannot continue" -ForegroundColor Red
            } else {
                Write-Host "    [WARNING] Optional module failed to load - some features may be unavailable" -ForegroundColor Yellow
            }

            $failedModules += @{
                Name = $moduleName
                Path = $moduleFullPath
                Error = $errorMsg
                ErrorType = $_.Exception.GetType().Name
                FullException = $_
                Critical = $isCritical
                Description = $description
            }
        }
    }

    # Report results
    Write-Host ""
    Write-Host "Module Loading Summary:" -ForegroundColor Cyan
    Write-Host "  Successfully loaded: $($loadedModules.Count)/$($modulesToLoad.Count)" -ForegroundColor Green

    # Check for critical module failures
    $criticalFailures = $failedModules | Where-Object { $_.Critical -eq $true }
    $optionalFailures = $failedModules | Where-Object { $_.Critical -eq $false }

    if ($failedModules.Count -gt 0) {
        Write-Host "  Failed to load: $($failedModules.Count)" -ForegroundColor Red

        if ($criticalFailures.Count -gt 0) {
            Write-Host "    Critical failures: $($criticalFailures.Count)" -ForegroundColor Red
        }
        if ($optionalFailures.Count -gt 0) {
            Write-Host "    Optional failures: $($optionalFailures.Count)" -ForegroundColor Yellow
        }

        Write-Host ""
        Write-Host "Failed Modules Details:" -ForegroundColor Red
        foreach ($failed in $failedModules) {
            $color = if ($failed.Critical) { "Red" } else { "Yellow" }
            $type = if ($failed.Critical) { "CRITICAL" } else { "OPTIONAL" }
            Write-Host "  - $($failed.Name) [$type]" -ForegroundColor $color
            Write-Host "    Description: $($failed.Description)" -ForegroundColor Gray
            Write-Host "    Path: $($failed.Path)" -ForegroundColor Gray
            Write-Host "    Error: $($failed.Error)" -ForegroundColor Gray
            Write-Host "    Type: $($failed.ErrorType)" -ForegroundColor Gray
        }

        # Determine overall success based on critical modules only
        $overallSuccess = $criticalFailures.Count -eq 0

        if ($overallSuccess) {
            Write-Host ""
            Write-Host "Core modules loaded successfully! Optional module failures will not prevent script execution." -ForegroundColor Green
            Write-Host "Some features may be unavailable due to optional module failures." -ForegroundColor Yellow
        }

        # Return result information
        return @{
            Success = $overallSuccess
            LoadedModules = $loadedModules
            FailedModules = $failedModules
            CriticalFailures = $criticalFailures
            OptionalFailures = $optionalFailures
            TotalModules = $modulesToLoad.Count
        }
    }

    Write-Host "All JML modules loaded successfully!" -ForegroundColor Green
    return @{
        Success = $true
        LoadedModules = $loadedModules
        FailedModules = @()
        CriticalFailures = @()
        OptionalFailures = @()
        TotalModules = $modulesToLoad.Count
    }
}

# Execute module loading
try {
    $ModulesDirectory = Join-Path $ScriptDirectory "Modules"
    $moduleLoadResult = Import-JMLModules -ModulesDirectory $ModulesDirectory

    if (-not $moduleLoadResult.Success) {
        Write-Host ""
        Write-Host "CRITICAL ERROR: One or more critical JML modules failed to load." -ForegroundColor Red
        Write-Host "The script cannot continue without all required modules." -ForegroundColor Red
        Write-Host ""
        Write-Host "Critical Module Failures:" -ForegroundColor Red
        foreach ($critical in $moduleLoadResult.CriticalFailures) {
            Write-Host "  - $($critical.Name): $($critical.Description)" -ForegroundColor Red
            Write-Host "    Error: $($critical.Error)" -ForegroundColor Gray
        }
        Write-Host ""
        Write-Host "Troubleshooting Steps:" -ForegroundColor Yellow
        Write-Host "1. Verify all module files exist in: $ModulesDirectory" -ForegroundColor Yellow
        Write-Host "2. Check file permissions and ensure files are not blocked" -ForegroundColor Yellow
        Write-Host "3. Run with -RunSetup to validate the environment" -ForegroundColor Yellow
        Write-Host "4. Check PowerShell execution policy: Get-ExecutionPolicy" -ForegroundColor Yellow
        Write-Host "5. For syntax errors in modules, check the module files for missing braces or invalid syntax" -ForegroundColor Yellow

        exit 1
    } elseif ($moduleLoadResult.OptionalFailures.Count -gt 0) {
        Write-Host ""
        Write-Host "WARNING: Some optional modules failed to load." -ForegroundColor Yellow
        Write-Host "The script will continue, but some features may be unavailable:" -ForegroundColor Yellow
        foreach ($optional in $moduleLoadResult.OptionalFailures) {
            Write-Host "  - $($optional.Name): $($optional.Description)" -ForegroundColor Yellow
            if ($optional.Name -eq "JML-ActiveDirectory.psm1") {
                Write-Host "    Impact: Active Directory operations will not be available" -ForegroundColor Gray
            } elseif ($optional.Name -eq "JML-Setup.psm1") {
                Write-Host "    Impact: Setup and validation utilities will not be available" -ForegroundColor Gray
            } elseif ($optional.Name -eq "JML-Email.psm1") {
                Write-Host "    Impact: Email notifications will not be sent" -ForegroundColor Gray
            } elseif ($optional.Name -eq "JML-Jira.psm1") {
                Write-Host "    Impact: Jira integration will not be available" -ForegroundColor Gray
            }
        }
        Write-Host ""
    }
}
catch {
    Write-Error "Critical error during module loading: $($_.Exception.Message)"
    Write-Host "Please ensure all module files are present and accessible." -ForegroundColor Red
    Write-Host "Module Directory: $ModulesDirectory" -ForegroundColor Gray
    exit 1
}

# Global script state
$script:Config = $null
$script:CurrentLogPath = $null
$script:JMLExecutionContext = @{
    StartTime = Get-Date
    ExecutingUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
    ComputerName = $env:COMPUTERNAME
    PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    ScriptPath = $PSCommandPath
    ScriptDirectory = $ScriptDirectory
    Version = $script:JMLVersion
    BuildDate = $script:JMLBuildDate
}

#endregion

#region Version and Setup Handling

<#
.SYNOPSIS
Gets the current version information for the JML system.

.DESCRIPTION
Returns version information for the JML system including module versions,
dependencies, and system information.

.OUTPUTS
Hashtable containing version and system information.

.EXAMPLE
$versionInfo = Get-JMLVersion

.NOTES
Provides comprehensive version and system information for troubleshooting.
#>
function Get-JMLVersion {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()

    try {
        $versionInfo = @{
            JMLVersion = "1.12"
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            OperatingSystem = [System.Environment]::OSVersion.ToString()
            ComputerName = $env:COMPUTERNAME
            CurrentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
            ScriptPath = $PSScriptRoot
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            Modules = @{}
        }

        # Check JML modules
        $jmlModules = @(
            "JML-Configuration", "JML-Security", "JML-Logging", "JML-Utilities",
            "JML-ActiveDirectory", "JML-Email", "JML-Jira", "JML-Setup"
        )

        foreach ($moduleName in $jmlModules) {
            $module = Get-Module -Name $moduleName -ErrorAction SilentlyContinue
            if ($module) {
                $versionInfo.Modules[$moduleName] = @{
                    Version = if ($module.Version) { $module.Version.ToString() } else { "1.12" }
                    Path = $module.Path
                    Loaded = $true
                    Functions = $module.ExportedFunctions.Count
                }
            } else {
                $versionInfo.Modules[$moduleName] = @{
                    Version = "Unknown"
                    Path = "Not loaded"
                    Loaded = $false
                    Functions = 0
                }
            }
        }

        return $versionInfo
    }
    catch {
        Write-Warning "Failed to get version information: $($_.Exception.Message)"
        return @{
            JMLVersion = "1.12"
            Error = $_.Exception.Message
        }
    }
}

# Handle version display
if ($ShowVersion) {
    $versionInfo = Get-JMLVersion
    
    Write-Host ""
    Write-Host "JML Admin Account Management System" -ForegroundColor Cyan
    Write-Host "Version: $($versionInfo.JMLVersion)" -ForegroundColor Green
    Write-Host "Build Date: $script:JMLBuildDate" -ForegroundColor Green
    Write-Host "PowerShell: $($versionInfo.PowerShellVersion)" -ForegroundColor Yellow
    Write-Host "Operating System: $($versionInfo.OperatingSystem)" -ForegroundColor Yellow
    Write-Host "Current User: $($versionInfo.CurrentUser)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Module Status:" -ForegroundColor Cyan
    
    foreach ($module in $versionInfo.Modules.GetEnumerator()) {
        $status = if ($module.Value.Loaded) { "[OK]" } else { "[MISSING]" }
        $color = if ($module.Value.Loaded) { "Green" } else { "Red" }
        Write-Host "  $($module.Key): $($module.Value.Version) $status" -ForegroundColor $color
    }
    
    Write-Host ""
    exit 0
}

# Handle setup mode
if ($RunSetup) {
    Write-Host ""
    Write-Host "JML Setup Utility" -ForegroundColor Cyan
    Write-Host "Version $script:JMLVersion" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Gray
    Write-Host ""
    
    $setupChoice = Read-Host @"
Select setup options:
1. Validate environment only
2. Setup credentials only  
3. Full setup (validate + credentials + install missing modules)
4. Exit

Enter your choice (1-4)
"@

    switch ($setupChoice) {
        "1" { 
            $result = Start-JMLSetup -ValidateEnvironment
        }
        "2" { 
            $result = Start-JMLSetup -SetupCredentials
        }
        "3" { 
            $result = Start-JMLSetup -ValidateEnvironment -SetupCredentials -InstallMissingModules
        }
        "4" { 
            Write-Host "Setup cancelled." -ForegroundColor Yellow
            exit 0
        }
        default { 
            Write-Host "Invalid choice. Exiting." -ForegroundColor Red
            exit 1
        }
    }
    
    if ($result) {
        Write-Host ""
        Write-Host "Setup completed successfully! You can now run the main script." -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "Setup completed with issues. Please review the messages above." -ForegroundColor Yellow
    }
    
    exit 0
}

#endregion

#region Configuration Initialization

try {
    Write-Progress -Activity "Initializing JML System" -Status "Loading configuration..." -PercentComplete 10

    # Ensure critical modules are loaded
    $criticalModules = @(
        "JML-Configuration.psm1",
        "JML-Security.psm1",
        "JML-Logging.psm1",
        "JML-Utilities.psm1"
    )

    foreach ($module in $criticalModules) {
        $modulePath = Join-Path $ModulesDirectory $module
        Import-Module $modulePath -Force -Global -ErrorAction SilentlyContinue
    }

    # Initialize configuration
    # Try calling with module prefix if direct call fails
    try {
        $configInitialized = Initialize-SmartConfiguration -ConfigPath $ConfigPath
    }
    catch {
        Write-Host "Direct call failed, re-importing module and trying again..." -ForegroundColor Yellow
        # Force re-import the configuration module
        $configModulePath = Join-Path $ModulesDirectory "JML-Configuration.psm1"
        Import-Module $configModulePath -Force -Global
        $configInitialized = Initialize-SmartConfiguration -ConfigPath $ConfigPath
    }
    
    if (-not $configInitialized) {
        throw "Failed to initialize configuration"
    }
    
    # Get the loaded configuration
    $script:Config = Get-ModuleConfiguration
    
    if (-not $script:Config) {
        throw "Configuration not available after initialization"
    }
    
    Write-Progress -Activity "Initializing JML System" -Status "Configuration loaded successfully" -PercentComplete 30
}
catch {
    Write-Error "Configuration initialization failed: $($_.Exception.Message)"
    Write-Host "Try running with -RunSetup to validate your environment." -ForegroundColor Yellow
    exit 1
}

#endregion

#region Helper Functions

<#
.SYNOPSIS
Ensures logging module is loaded and calls Write-SecureLog.

.DESCRIPTION
This function ensures the JML-Logging module is loaded before calling Write-SecureLog.
It provides a workaround for module loading issues.

.PARAMETER Message
The message to log.

.PARAMETER LogLevel
The log level.

.PARAMETER AuditTrail
Optional audit trail information.

.EXAMPLE
Invoke-SecureLog -Message "Test message" -LogLevel "INFO"

.NOTES
This is a workaround for module scoping issues.
#>
function Invoke-SecureLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $true)]
        [string]$LogLevel,

        [Parameter(Mandatory = $false)]
        [hashtable]$AuditTrail
    )

    try {
        # Check if Write-SecureLog is available
        $logFunction = Get-Command Write-SecureLog -ErrorAction SilentlyContinue

        if (-not $logFunction) {
            # Re-import the logging module
            $loggingModulePath = Join-Path $ModulesDirectory "JML-Logging.psm1"
            Import-Module $loggingModulePath -Force -Global -ErrorAction SilentlyContinue
        }

        # Try to call Write-SecureLog
        if ($AuditTrail) {
            Write-SecureLog -Message $Message -LogLevel $LogLevel -AuditTrail $AuditTrail
        } else {
            Write-SecureLog -Message $Message -LogLevel $LogLevel
        }
    }
    catch {
        # Fallback to basic logging if Write-SecureLog fails
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] [$LogLevel] $Message" -ForegroundColor Gray
    }
}

<#
.SYNOPSIS
Basic setup function to replace the missing JML-Setup module functionality.

.DESCRIPTION
Provides basic setup functionality when the JML-Setup module is not available.

.PARAMETER ValidateEnvironment
Switch to validate the environment.

.PARAMETER SetupCredentials
Switch to setup credentials.

.PARAMETER InstallMissingModules
Switch to install missing modules.

.OUTPUTS
Boolean indicating if setup completed successfully.

.EXAMPLE
$success = Start-JMLSetup -ValidateEnvironment

.NOTES
This is a basic implementation to replace the missing JML-Setup module.
#>
function Start-JMLSetup {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [switch]$ValidateEnvironment,

        [Parameter(Mandatory = $false)]
        [switch]$SetupCredentials,

        [Parameter(Mandatory = $false)]
        [switch]$InstallMissingModules
    )

    try {
        Write-Host "JML Setup Utility (Basic Mode)" -ForegroundColor Cyan
        Write-Host "Note: Full setup functionality requires the JML-Setup module." -ForegroundColor Yellow
        Write-Host ""

        $success = $true

        if ($ValidateEnvironment) {
            Write-Host "Environment Validation:" -ForegroundColor Green

            # Check PowerShell version
            if ($PSVersionTable.PSVersion.Major -ge 5) {
                Write-Host "  [OK] PowerShell version: $($PSVersionTable.PSVersion)" -ForegroundColor Green
            } else {
                Write-Host "  [ERROR] PowerShell version: $($PSVersionTable.PSVersion) (5.1 or higher required)" -ForegroundColor Red
                $success = $false
            }

            # Check if ActiveDirectory module is available
            $adModule = Get-Module -ListAvailable -Name ActiveDirectory -ErrorAction SilentlyContinue
            if ($adModule) {
                Write-Host "  [OK] ActiveDirectory module: Available" -ForegroundColor Green
            } else {
                Write-Host "  [WARNING] ActiveDirectory module: Not available (required for full functionality)" -ForegroundColor Yellow
            }

            # Check configuration file
            $configPath = Join-Path $ScriptDirectory "AdminAccountConfig.psd1"
            if (Test-Path $configPath) {
                Write-Host "  [OK] Configuration file: Found" -ForegroundColor Green
            } else {
                Write-Host "  [WARNING] Configuration file: Not found (using defaults)" -ForegroundColor Yellow
            }

            Write-Host ""
        }

        if ($SetupCredentials) {
            Write-Host "Credential Setup:" -ForegroundColor Green
            Write-Host "  [WARNING] Credential setup requires the full JML-Setup module" -ForegroundColor Yellow
            Write-Host "  [INFO] You can manually configure credentials using Windows Credential Manager" -ForegroundColor Cyan
            Write-Host ""
        }

        if ($InstallMissingModules) {
            Write-Host "Module Installation:" -ForegroundColor Green
            Write-Host "  [WARNING] Module installation requires the full JML-Setup module" -ForegroundColor Yellow
            Write-Host "  [INFO] You can manually install modules using Install-Module" -ForegroundColor Cyan
            Write-Host ""
        }

        if ($success) {
            Write-Host "Basic setup validation completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "Setup validation found issues that need to be resolved." -ForegroundColor Yellow
        }

        return $success
    }
    catch {
        Write-Host "Setup failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

<#
.SYNOPSIS
Wrapper function to ensure Get-ValidatedUPN is available and calls it.

.DESCRIPTION
This function ensures the JML-Utilities module is loaded before calling Get-ValidatedUPN.
It provides a workaround for module loading issues.

.PARAMETER Domain
The domain to validate against.

.PARAMETER ForDeletion
Switch indicating this is for account deletion.

.PARAMETER ForReset
Switch indicating this is for password reset.

.EXAMPLE
$upn = Invoke-ValidatedUPN -Domain "jeragm.com"

.NOTES
This is a workaround for module scoping issues.
#>
function Invoke-ValidatedUPN {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset
    )

    try {
        # Set appropriate prompt message based on context
        $promptMessage = if ($ForDeletion) {
            "Enter the UPN of the user whose admin account should be deleted"
        } elseif ($ForReset) {
            "Enter the UPN of the user whose admin account password should be reset"
        } else {
            "Enter User Principal Name (UPN)"
        }

        # Get expected domain
        $expectedDomain = if ($Domain) { $Domain } else { "jeragm.com" }

        do {
            $upn = Read-Host $promptMessage

            # Handle empty input
            if ([string]::IsNullOrWhiteSpace($upn)) {
                Write-Host "UPN cannot be empty. Please enter a valid UPN." -ForegroundColor Yellow
                continue
            }

            # Validate UPN format
            if ($upn -notmatch '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$') {
                Write-Host "Invalid UPN format. Please enter a valid email-like UPN (e.g., <EMAIL>)." -ForegroundColor Yellow
                continue
            }

            # Extract domain from UPN
            $upnDomain = $upn.Split('@')[1]

            # Validate domain
            if ($upnDomain -ne $expectedDomain) {
                Write-Host "UPN domain '$upnDomain' does not match expected domain '$expectedDomain'." -ForegroundColor Yellow
                $continue = Read-Host "Do you want to continue anyway? (y/N)"
                if ($continue -notmatch '^[Yy]') {
                    continue
                }
            }

            # Return validated UPN
            return $upn

        } while ($true)
    }
    catch {
        Write-Host "UPN validation failed: $($_.Exception.Message)" -ForegroundColor Red
        # Fallback to basic input
        $promptMessage = if ($ForDeletion) {
            "Enter the UPN of the user whose admin account should be deleted"
        } elseif ($ForReset) {
            "Enter the UPN of the user whose admin account password should be reset"
        } else {
            "Enter User Principal Name (UPN)"
        }

        return Read-Host $promptMessage
    }
}

#endregion

#region Core Admin Account Functions

<#
.SYNOPSIS
Creates a new admin account based on a standard user's details.

.DESCRIPTION
Orchestrates the complete admin account creation process including user validation,
Jira ticket processing, account creation, email notifications, and audit logging.

.PARAMETER TicketKey
Optional Jira ticket key for automated processing.

.EXAMPLE
New-AdminAccount -TicketKey "HELP-12345"

.NOTES
This is the main orchestration function that coordinates all modules.
#>
function New-AdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey
    )

    try {
        Write-Host ""
        Write-Host "=== ADMIN ACCOUNT CREATION ===" -ForegroundColor Cyan
        Write-Host "JML System v$script:JMLVersion" -ForegroundColor Green
        Write-Host ""

        # Initialize logging for this operation
        $standardUserUPN = if ($TicketKey) {
            # Get UPN from Jira ticket if provided
            Write-Host "Processing Jira ticket: $TicketKey" -ForegroundColor Cyan

            if (-not $SkipJiraIntegration) {
                # Initialize Jira connection
                $jiraCredential = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"
                $jiraUsername = Get-SecureCredential -CredentialName "JiraUsername" -Purpose "Jira username"

                if ($jiraCredential -and $jiraUsername) {
                    $jiraCred = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraCredential)
                    Initialize-JiraConnection -ServerUrl $script:Config.Jira.ServerUrl -Credential $jiraCred -TestConnection

                    # Validate ticket
                    $ticketValidation = Test-JiraTicketValidation -TicketKey $TicketKey -ExpectedWorkType $script:Config.Jira.ExpectedWorkTypes.CreateAdmin

                    if (-not $ticketValidation.IsValid) {
                        throw "Jira ticket validation failed: $($ticketValidation.ErrorMessage)"
                    }

                    # Extract UPN from ticket
                    $firstName = $ticketValidation.Fields.FirstName
                    $lastName = $ticketValidation.Fields.LastName

                    if ($firstName -and $lastName) {
                        New-StandardUPN -FirstName $firstName -LastName $lastName -Domain $script:Config.ScriptSettings.DefaultDomain
                    } else {
                        throw "Could not extract user details from Jira ticket"
                    }
                } else {
                    throw "Jira credentials not available. Run setup to configure credentials."
                }
            } else {
                Read-Host "Enter the UPN of the user for admin account creation"
            }
        } else {
            # Interactive mode
            Invoke-ValidatedUPN -Domain $script:Config.ScriptSettings.DefaultDomain
        }

        # Initialize secure logging
        $script:CurrentLogPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN

        Write-SecureLog -Message "Starting admin account creation process" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreationStart"
            TargetUser = Get-StringHash -InputString $standardUserUPN
            TicketKey = $TicketKey
            ExecutingUser = $script:JMLExecutionContext.ExecutingUser
            Version = $script:JMLVersion
        }

        # Get user details from Active Directory
        Write-Host "Retrieving user details from Active Directory..." -ForegroundColor Yellow
        $standardUser = Get-OptimizedADUserDetails -UserUPN $standardUserUPN -UseCache

        if (-not $standardUser) {
            throw "User not found in Active Directory: $standardUserUPN"
        }

        Write-SecureLog -Message "Standard user found in Active Directory" -LogLevel "INFO" -AuditTrail @{
            Operation = "StandardUserFound"
            UserDisplayName = Protect-SensitiveData -Text $standardUser.DisplayName
            UserEnabled = $standardUser.Enabled
        }

        # Check if admin account already exists
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $existingAdminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if ($existingAdminAccount) {
            $message = "Admin account already exists for this user: $adminSamAccountName"
            Write-Host $message -ForegroundColor Red
            Write-SecureLog -Message $message -LogLevel "ERROR"

            if ($TicketKey -and -not $SkipJiraIntegration) {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account creation failed: Account already exists ($adminSamAccountName)" -UseADF -IncludeTimestamp
            }

            return $false
        }

        # Select OU for admin account
        $officeLocation = if ($TicketKey -and $ticketValidation.Fields.OfficeLocation) {
            $ticketValidation.Fields.OfficeLocation
        } else {
            $standardUser.Office
        }

        $targetOU = Select-OU -OfficeLocation $officeLocation

        Write-SecureLog -Message "Target OU selected for admin account" -LogLevel "INFO" -AuditTrail @{
            Operation = "OUSelection"
            TargetOU = Protect-SensitiveData -Text $targetOU
            OfficeLocation = $officeLocation
        }

        # Generate secure password
        Write-Host "Generating secure password..." -ForegroundColor Yellow
        $adminPassword = New-SecurePassword

        # Create admin account UPN
        $adminUserUPN = New-StandardUPN -FirstName $standardUser.GivenName -LastName $standardUser.Surname -Domain $script:Config.ScriptSettings.DefaultDomain
        $adminUserUPN = $adminUserUPN -replace "@", "-a@"

        Write-Host "Creating admin account..." -ForegroundColor Yellow
        Write-SecureLog -Message "Creating admin account in Active Directory" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreation"
            AdminSamAccountName = $adminSamAccountName
            AdminUPN = Get-StringHash -InputString $adminUserUPN
            TargetOU = Get-StringHash -InputString $targetOU
        }

        # Create the admin account
        $adminAccountParams = @{
            Name = "$($standardUser.DisplayName) (Admin)"
            SamAccountName = $adminSamAccountName
                UserPrincipalName  = $adminUserUPN
            GivenName = $standardUser.GivenName
            Surname = $standardUser.Surname
            DisplayName = "$($standardUser.DisplayName) (Admin)"
            Description = "Admin account for $($standardUser.DisplayName)"
            Path = $targetOU
            AccountPassword = (ConvertTo-SecureString $adminPassword -AsPlainText -Force)
            Enabled = $true
            ChangePasswordAtLogon = $script:Config.ActiveDirectory.PasswordPolicy.ChangePasswordAtLogon
            PasswordNeverExpires = $script:Config.ActiveDirectory.PasswordPolicy.PasswordNeverExpires
        }

        New-ADUser @adminAccountParams -ErrorAction Stop

        Write-Host "Admin account created successfully: $adminSamAccountName" -ForegroundColor Green
        Write-SecureLog -Message "Admin account created successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreated"
            AdminSamAccountName = $adminSamAccountName
            Success = $true
        }

        # Send email notifications
        if ($script:Config.Email.EnableNotifications) {
            Write-Host "Sending email notifications..." -ForegroundColor Yellow
            try {
                Send-EmailNotification -LogPath $script:CurrentLogPath -AdminUserUPN $adminUserUPN -Password $adminPassword -StandardUserEmail $standardUser.EmailAddress
                Write-SecureLog -Message "Email notifications sent successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to send email notifications: $($_.Exception.Message)"
                Write-SecureLog -Message "Email notification failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        # Update Jira ticket
        if ($TicketKey -and -not $SkipJiraIntegration) {
            Write-Host "Updating Jira ticket..." -ForegroundColor Yellow
            try {
                $jiraComment = @"
[SUCCESS] Admin Account Created Successfully

Account Details:
- Admin Username: $adminSamAccountName
- Admin UPN: $adminUserUPN
- Target OU: $(Split-Path $targetOU -Leaf)
- Created By: $(Get-CurrentUserName)
- Creation Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Next Steps:
- Password has been sent to the user via email
- User should change password on first login
- Account is ready for use

This is an automated update from the JML Admin Account System v$script:JMLVersion
"@

                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText $jiraComment -UseADF -IncludeTimestamp
                Add-EnhancedJiraAttachment -TicketKey $TicketKey -FilePath $script:CurrentLogPath

                Write-SecureLog -Message "Jira ticket updated successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to update Jira ticket: $($_.Exception.Message)"
                Write-SecureLog -Message "Jira update failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        Write-Host ""
        Write-Host "Admin account creation completed successfully!" -ForegroundColor Green
        Write-Host "Admin Username: $adminSamAccountName" -ForegroundColor Cyan
        Write-Host "Admin UPN: $adminUserUPN" -ForegroundColor Cyan
        Write-Host "Log file: $script:CurrentLogPath" -ForegroundColor Gray
        Write-Host ""

        Write-SecureLog -Message "Admin account creation process completed successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreationComplete"
            Success = $true
            Duration = ((Get-Date) - $script:JMLExecutionContext.StartTime).TotalSeconds
        }

        return $true
    }
    catch {
        $errorMessage = "Admin account creation failed: $($_.Exception.Message)"
        Write-Host $errorMessage -ForegroundColor Red
        Write-SecureLog -Message $errorMessage -LogLevel "ERROR" -AuditTrail @{
            Operation = "AdminAccountCreationError"
            ErrorType = $_.Exception.GetType().Name
            Success = $false
        }

        # Update Jira ticket with error
        if ($TicketKey -and -not $SkipJiraIntegration) {
            try {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account creation failed: $($_.Exception.Message)" -UseADF -IncludeTimestamp
            }
            catch {
                Write-Warning "Failed to update Jira ticket with error: $($_.Exception.Message)"
            }
        }

        return $false
    }
}

<#
.SYNOPSIS
Removes an existing admin account.

.DESCRIPTION
Orchestrates the complete admin account deletion process including validation,
Jira ticket processing, account deletion, email notifications, and audit logging.

.PARAMETER TicketKey
Optional Jira ticket key for automated processing.

.EXAMPLE
Remove-StdAdminAccount -TicketKey "HELP-12345"

.NOTES
This function safely removes admin accounts with comprehensive logging.
#>
function Remove-StdAdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey
    )

    try {
        Write-Host ""
        Write-Host "=== ADMIN ACCOUNT DELETION ===" -ForegroundColor Red
        Write-Host "JML System v$script:JMLVersion" -ForegroundColor Green
        Write-Host ""

        # Get UPN for deletion
        $standardUserUPN = if ($TicketKey) {
            Write-Host "Processing Jira ticket: $TicketKey" -ForegroundColor Cyan

            if (-not $SkipJiraIntegration) {
                # Initialize Jira and extract UPN from ticket
                $jiraCredential = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"
                $jiraUsername = Get-SecureCredential -CredentialName "JiraUsername" -Purpose "Jira username"

                if ($jiraCredential -and $jiraUsername) {
                    $jiraCred = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraCredential)
                    Initialize-JiraConnection -ServerUrl $script:Config.Jira.ServerUrl -Credential $jiraCred -TestConnection

                    $ticketValidation = Test-JiraTicketValidation -TicketKey $TicketKey -ExpectedWorkType $script:Config.Jira.ExpectedWorkTypes.DeleteAdmin

                    if (-not $ticketValidation.IsValid) {
                        throw "Jira ticket validation failed: $($ticketValidation.ErrorMessage)"
                    }

                    # Extract UPN from ticket or prompt
                    if ($ticketValidation.Fields.FirstName -and $ticketValidation.Fields.LastName) {
                        New-StandardUPN -FirstName $ticketValidation.Fields.FirstName -LastName $ticketValidation.Fields.LastName -Domain $script:Config.ScriptSettings.DefaultDomain
                    } else {
                        Read-Host "Enter the UPN of the user whose admin account should be deleted"
                    }
                } else {
                    throw "Jira credentials not available. Run setup to configure credentials."
                }
            } else {
                Read-Host "Enter the UPN of the user whose admin account should be deleted"
            }
        } else {
            Invoke-ValidatedUPN -Domain $script:Config.ScriptSettings.DefaultDomain -ForDeletion
        }

        # Initialize secure logging
        $script:CurrentLogPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN

        Write-SecureLog -Message "Starting admin account deletion process" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeletionStart"
            TargetUser = Get-StringHash -InputString $standardUserUPN
            TicketKey = $TicketKey
            ExecutingUser = $script:JMLExecutionContext.ExecutingUser
            Version = $script:JMLVersion
        }

        # Get standard user details
        $standardUser = Get-OptimizedADUserDetails -UserUPN $standardUserUPN -UseCache
        if (-not $standardUser) {
            throw "Standard user not found in Active Directory: $standardUserUPN"
        }

        # Find admin account
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if (-not $adminAccount) {
            $message = "No admin account found for user: $standardUserUPN"
            Write-Host $message -ForegroundColor Yellow
            Write-SecureLog -Message $message -LogLevel "WARNING"

            if ($TicketKey -and -not $SkipJiraIntegration) {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[WARNING] Admin account deletion: No admin account found for this user" -UseADF -IncludeTimestamp
            }

            return $false
        }

        # Confirm deletion
        Write-Host "Admin account found: $($adminAccount.DisplayName)" -ForegroundColor Yellow
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Yellow
        Write-Host "UPN: $($adminAccount.UserPrincipalName)" -ForegroundColor Yellow
        Write-Host ""

        if (-not $TicketKey) {
            $confirmation = Read-Host "Are you sure you want to DELETE this admin account? (Type 'DELETE' to confirm)"
            if ($confirmation -ne 'DELETE') {
                Write-Host "Deletion cancelled by user." -ForegroundColor Yellow
                return $false
            }
        }

        # Delete the admin account
        Write-Host "Deleting admin account..." -ForegroundColor Red
        Write-SecureLog -Message "Deleting admin account from Active Directory" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeletion"
            AdminSamAccountName = $adminAccount.SamAccountName
            AdminUPN = Get-StringHash -InputString $adminAccount.UserPrincipalName
        }

        Remove-ADUser -Identity $adminAccount.SamAccountName -Confirm:$false -ErrorAction Stop

        Write-Host "Admin account deleted successfully: $($adminAccount.SamAccountName)" -ForegroundColor Green
        Write-SecureLog -Message "Admin account deleted successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeleted"
            AdminSamAccountName = $adminAccount.SamAccountName
            Success = $true
        }

        # Send email notification
        if ($script:Config.Email.EnableNotifications) {
            Write-Host "Sending email notification..." -ForegroundColor Yellow
            try {
                Send-DeletionEmailNotification -LogPath $script:CurrentLogPath -AdminUserUPN $adminAccount.UserPrincipalName -StandardUserEmail $standardUser.EmailAddress
                Write-SecureLog -Message "Deletion email notification sent successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to send email notification: $($_.Exception.Message)"
                Write-SecureLog -Message "Email notification failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        # Update Jira ticket
        if ($TicketKey -and -not $SkipJiraIntegration) {
            Write-Host "Updating Jira ticket..." -ForegroundColor Yellow
            try {
                $jiraComment = @"
[SUCCESS] Admin Account Deleted Successfully

Deleted Account Details:
- Admin Username: $($adminAccount.SamAccountName)
- Admin UPN: $($adminAccount.UserPrincipalName)
- Deleted By: $(Get-CurrentUserName)
- Deletion Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Action Completed:
- Admin account has been permanently removed from Active Directory
- Email notification sent to support team
- All access associated with this admin account has been revoked

This is an automated update from the JML Admin Account System v$script:JMLVersion
"@

                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText $jiraComment -UseADF -IncludeTimestamp
                Add-EnhancedJiraAttachment -TicketKey $TicketKey -FilePath $script:CurrentLogPath

                Write-SecureLog -Message "Jira ticket updated successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to update Jira ticket: $($_.Exception.Message)"
                Write-SecureLog -Message "Jira update failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        Write-Host ""
        Write-Host "Admin account deletion completed successfully!" -ForegroundColor Green
        Write-Host "Deleted Account: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "Log file: $script:CurrentLogPath" -ForegroundColor Gray
        Write-Host ""

        Write-SecureLog -Message "Admin account deletion process completed successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeletionComplete"
            Success = $true
            Duration = ((Get-Date) - $script:JMLExecutionContext.StartTime).TotalSeconds
        }

        return $true
    }
    catch {
        $errorMessage = "Admin account deletion failed: $($_.Exception.Message)"
        Write-Host $errorMessage -ForegroundColor Red
        Write-SecureLog -Message $errorMessage -LogLevel "ERROR" -AuditTrail @{
            Operation = "AdminAccountDeletionError"
            ErrorType = $_.Exception.GetType().Name
            Success = $false
        }

        # Update Jira ticket with error
        if ($TicketKey -and -not $SkipJiraIntegration) {
            try {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account deletion failed: $($_.Exception.Message)" -UseADF -IncludeTimestamp
            }
            catch {
                Write-Warning "Failed to update Jira ticket with error: $($_.Exception.Message)"
            }
        }

        return $false
    }
}

<#
.SYNOPSIS
Resets the password for an existing admin account.

.DESCRIPTION
Orchestrates the complete admin account password reset process including validation,
Jira ticket processing, password reset, email notifications, and audit logging.

.PARAMETER TicketKey
Optional Jira ticket key for automated processing.

.EXAMPLE
Reset-StdAdminAccount -TicketKey "HELP-12345"

.NOTES
This function safely resets admin account passwords with comprehensive logging.
#>
function Reset-StdAdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey
    )

    try {
        Write-Host ""
        Write-Host "=== ADMIN ACCOUNT PASSWORD RESET ===" -ForegroundColor Yellow
        Write-Host "JML System v$script:JMLVersion" -ForegroundColor Green
        Write-Host ""

        # Get UPN for reset
        $standardUserUPN = if ($TicketKey) {
            Write-Host "Processing Jira ticket: $TicketKey" -ForegroundColor Cyan

            if (-not $SkipJiraIntegration) {
                # Initialize Jira and extract UPN from ticket
                $jiraCredential = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"
                $jiraUsername = Get-SecureCredential -CredentialName "JiraUsername" -Purpose "Jira username"

                if ($jiraCredential -and $jiraUsername) {
                    $jiraCred = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraCredential)
                    Initialize-JiraConnection -ServerUrl $script:Config.Jira.ServerUrl -Credential $jiraCred -TestConnection

                    $ticketValidation = Test-JiraTicketValidation -TicketKey $TicketKey -ExpectedWorkType $script:Config.Jira.ExpectedWorkTypes.ResetAdmin

                    if (-not $ticketValidation.IsValid) {
                        throw "Jira ticket validation failed: $($ticketValidation.ErrorMessage)"
                    }

                    # Extract UPN from ticket or prompt
                    if ($ticketValidation.Fields.FirstName -and $ticketValidation.Fields.LastName) {
                        New-StandardUPN -FirstName $ticketValidation.Fields.FirstName -LastName $ticketValidation.Fields.LastName -Domain $script:Config.ScriptSettings.DefaultDomain
                    } else {
                        Read-Host "Enter the UPN of the user whose admin account password should be reset"
                    }
                } else {
                    throw "Jira credentials not available. Run setup to configure credentials."
                }
            } else {
                Read-Host "Enter the UPN of the user whose admin account password should be reset"
            }
        } else {
            Invoke-ValidatedUPN -Domain $script:Config.ScriptSettings.DefaultDomain -ForReset
        }

        # Initialize secure logging
        $script:CurrentLogPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN

        Write-SecureLog -Message "Starting admin account password reset process" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountResetStart"
            TargetUser = Get-StringHash -InputString $standardUserUPN
            TicketKey = $TicketKey
            ExecutingUser = $script:JMLExecutionContext.ExecutingUser
            Version = $script:JMLVersion
        }

        # Get standard user details
        $standardUser = Get-OptimizedADUserDetails -UserUPN $standardUserUPN -UseCache
        if (-not $standardUser) {
            throw "Standard user not found in Active Directory: $standardUserUPN"
        }

        # Find admin account
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if (-not $adminAccount) {
            $message = "No admin account found for user: $standardUserUPN"
            Write-Host $message -ForegroundColor Yellow
            Write-SecureLog -Message $message -LogLevel "WARNING"

            if ($TicketKey -and -not $SkipJiraIntegration) {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[WARNING] Admin account password reset: No admin account found for this user" -UseADF -IncludeTimestamp
            }

            return $false
        }

        # Display account info
        Write-Host "Admin account found: $($adminAccount.DisplayName)" -ForegroundColor Yellow
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Yellow
        Write-Host "UPN: $($adminAccount.UserPrincipalName)" -ForegroundColor Yellow
        Write-Host ""

        # Generate new password
        Write-Host "Generating new secure password..." -ForegroundColor Yellow
        $newPassword = New-SecurePassword

        # Reset password
        Write-Host "Resetting admin account password..." -ForegroundColor Yellow
        Write-SecureLog -Message "Resetting admin account password" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountPasswordReset"
            AdminSamAccountName = $adminAccount.SamAccountName
            AdminUPN = Get-StringHash -InputString $adminAccount.UserPrincipalName
        }

        Set-ADAccountPassword -Identity $adminAccount.SamAccountName -NewPassword (ConvertTo-SecureString $newPassword -AsPlainText -Force) -Reset -ErrorAction Stop
        Set-ADUser -Identity $adminAccount.SamAccountName -ChangePasswordAtLogon $script:Config.ActiveDirectory.PasswordPolicy.ChangePasswordAtLogon -ErrorAction Stop

        Write-Host "Admin account password reset successfully: $($adminAccount.SamAccountName)" -ForegroundColor Green
        Write-SecureLog -Message "Admin account password reset successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountPasswordResetComplete"
            AdminSamAccountName = $adminAccount.SamAccountName
            Success = $true
        }

        # Send email notification
        if ($script:Config.Email.EnableNotifications) {
            Write-Host "Sending email notification..." -ForegroundColor Yellow
            try {
                Send-ResetEmailNotification -LogPath $script:CurrentLogPath -AdminUserUPN $adminAccount.UserPrincipalName -NewPassword $newPassword -StandardUserEmail $standardUser.EmailAddress
                Write-SecureLog -Message "Reset email notification sent successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to send email notification: $($_.Exception.Message)"
                Write-SecureLog -Message "Email notification failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        # Update Jira ticket
        if ($TicketKey -and -not $SkipJiraIntegration) {
            Write-Host "Updating Jira ticket..." -ForegroundColor Yellow
            try {
                $jiraComment = @"
[SUCCESS] Admin Account Password Reset Successfully

Reset Account Details:
- Admin Username: $($adminAccount.SamAccountName)
- Admin UPN: $($adminAccount.UserPrincipalName)
- Reset By: $(try { Get-CurrentUserName } catch { $script:JMLExecutionContext.ExecutingUser })
- Reset Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Action Completed:
- New secure password has been generated and set
- Password change required on next login
- New credentials sent to user via email
- Account is ready for use

This is an automated update from the JML Admin Account System v$script:JMLVersion
"@

                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText $jiraComment -UseADF -IncludeTimestamp
                Add-EnhancedJiraAttachment -TicketKey $TicketKey -FilePath $script:CurrentLogPath

                Write-SecureLog -Message "Jira ticket updated successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to update Jira ticket: $($_.Exception.Message)"
                Write-SecureLog -Message "Jira update failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        Write-Host ""
        Write-Host "Admin account password reset completed successfully!" -ForegroundColor Green
        Write-Host "Account: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "New password has been sent to the user via email." -ForegroundColor Cyan
        Write-Host "Log file: $script:CurrentLogPath" -ForegroundColor Gray
        Write-Host ""

        Write-SecureLog -Message "Admin account password reset process completed successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountResetComplete"
            Success = $true
            Duration = ((Get-Date) - $script:JMLExecutionContext.StartTime).TotalSeconds
        }

        return $true
    }
    catch {
        $errorMessage = "Admin account password reset failed: $($_.Exception.Message)"
        Write-Host $errorMessage -ForegroundColor Red
        Write-SecureLog -Message $errorMessage -LogLevel "ERROR" -AuditTrail @{
            Operation = "AdminAccountResetError"
            ErrorType = $_.Exception.GetType().Name
            Success = $false
        }

        # Update Jira ticket with error
        if ($TicketKey -and -not $SkipJiraIntegration) {
            try {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account password reset failed: $($_.Exception.Message)" -UseADF -IncludeTimestamp
            }
            catch {
                Write-Warning "Failed to update Jira ticket with error: $($_.Exception.Message)"
            }
        }

        return $false
    }
}

#endregion

#region Main Menu and Execution Logic

<#
.SYNOPSIS
Displays the main menu for the JML system.

.DESCRIPTION
Shows an interactive menu with all available options and system information.

.NOTES
Provides a user-friendly interface for all JML operations.
#>
function Show-MainMenu {
    [CmdletBinding()]
    param()

    Clear-Host

    Write-Host ""
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host "                    JML Admin Account Management System                      " -ForegroundColor Cyan
    Write-Host "                              Version $script:JMLVersion                                    " -ForegroundColor Cyan
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Current User: " -NoNewline -ForegroundColor Gray
    Write-Host $script:JMLExecutionContext.ExecutingUser -ForegroundColor Yellow
    Write-Host "Computer: " -NoNewline -ForegroundColor Gray
    Write-Host $script:JMLExecutionContext.ComputerName -ForegroundColor Yellow
    Write-Host "Configuration: " -NoNewline -ForegroundColor Gray
    Write-Host "Loaded" -ForegroundColor Green

    if ($SkipJiraIntegration) {
        Write-Host "Jira Integration: " -NoNewline -ForegroundColor Gray
        Write-Host "DISABLED" -ForegroundColor Red
    } else {
        Write-Host "Jira Integration: " -NoNewline -ForegroundColor Gray
        Write-Host "Enabled" -ForegroundColor Green
    }

    Write-Host ""
    Write-Host "Available Operations:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  1. Create Admin Account" -ForegroundColor Green
    Write-Host "     Create a new admin account for a standard user"
    Write-Host ""
    Write-Host "  2. Delete Admin Account" -ForegroundColor Red
    Write-Host "     Remove an existing admin account"
    Write-Host ""
    Write-Host "  3. Reset Admin Account Password" -ForegroundColor Yellow
    Write-Host "     Reset the password for an existing admin account"
    Write-Host ""
    Write-Host "  4. System Information" -ForegroundColor Cyan
    Write-Host "     Display version and module information"
    Write-Host ""
    Write-Host "  5. Run Setup" -ForegroundColor Magenta
    Write-Host "     Configure credentials and validate environment"
    Write-Host ""
    Write-Host "  6. Exit" -ForegroundColor Gray
    Write-Host "     Exit the application"
    Write-Host ""
    Write-Host "Enter your choice (1-6): " -NoNewline -ForegroundColor White
}

<#
.SYNOPSIS
Displays system information and module status.

.DESCRIPTION
Shows comprehensive system information including version details,
module status, and configuration information.

.NOTES
Useful for troubleshooting and system verification.
#>
function Show-SystemInformation {
    [CmdletBinding()]
    param()

    Clear-Host

    Write-Host ""
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host "                           System Information                                " -ForegroundColor Cyan
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host ""

    $versionInfo = Get-JMLVersion

    Write-Host "JML System Information:" -ForegroundColor Green
    Write-Host "  Version: $($versionInfo.JMLVersion)" -ForegroundColor White
    Write-Host "  Build Date: $script:JMLBuildDate" -ForegroundColor White
    Write-Host "  Script Path: $($versionInfo.ScriptPath)" -ForegroundColor Gray
    Write-Host ""

    Write-Host "Environment Information:" -ForegroundColor Green
    Write-Host "  PowerShell Version: $($versionInfo.PowerShellVersion)" -ForegroundColor White
    Write-Host "  Operating System: $($versionInfo.OperatingSystem)" -ForegroundColor White
    Write-Host "  Current User: $($versionInfo.CurrentUser)" -ForegroundColor White
    Write-Host "  Computer Name: $($versionInfo.ComputerName)" -ForegroundColor White
    Write-Host ""

    Write-Host "Module Status:" -ForegroundColor Green
    foreach ($module in $versionInfo.Modules.GetEnumerator() | Sort-Object Key) {
        $status = if ($module.Value.Loaded) { "[OK] Loaded" } else { "[MISSING] Not Loaded" }
        $color = if ($module.Value.Loaded) { "Green" } else { "Red" }
        Write-Host "  $($module.Key): " -NoNewline -ForegroundColor White
        Write-Host "$($module.Value.Version) " -NoNewline -ForegroundColor Gray
        Write-Host $status -ForegroundColor $color
    }

    Write-Host ""
    Write-Host "Configuration Status:" -ForegroundColor Green
    if ($script:Config) {
        Write-Host "  Configuration File: " -NoNewline -ForegroundColor White
        Write-Host "[OK] Loaded" -ForegroundColor Green
        Write-Host "  Default Domain: " -NoNewline -ForegroundColor White
        Write-Host $script:Config.ScriptSettings.DefaultDomain -ForegroundColor Yellow
        Write-Host "  Log Directory: " -NoNewline -ForegroundColor White
        Write-Host $script:Config.Logging.LogDirectory -ForegroundColor Yellow
        Write-Host "  Data Redaction: " -NoNewline -ForegroundColor White
        $redactionStatus = if ($script:Config.Logging.EnableDataRedaction) { "[OK] Enabled" } else { "Disabled" }
        $redactionColor = if ($script:Config.Logging.EnableDataRedaction) { "Green" } else { "Yellow" }
        Write-Host $redactionStatus -ForegroundColor $redactionColor
    } else {
        Write-Host "  Configuration: " -NoNewline -ForegroundColor White
        Write-Host "[ERROR] Not Loaded" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "Press any key to return to main menu..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

<#
.SYNOPSIS
Main execution logic for the JML system.

.DESCRIPTION
Handles the main menu loop and user interaction for the JML system.

.NOTES
This is the main entry point for interactive mode.
#>
function Start-MainExecution {
    [CmdletBinding()]
    param()

    try {
        Write-Progress -Activity "Initializing JML System" -Status "Starting main execution..." -PercentComplete 90

        # Ensure critical modules are loaded before main execution
        $criticalModules = @(
            "JML-Configuration.psm1",
            "JML-Security.psm1",
            "JML-Logging.psm1",
            "JML-Utilities.psm1"
        )

        foreach ($module in $criticalModules) {
            $modulePath = Join-Path $ModulesDirectory $module
            Import-Module $modulePath -Force -Global -ErrorAction SilentlyContinue
        }

        # Log system startup
        Invoke-SecureLog -Message "JML System started in interactive mode" -LogLevel "INFO" -AuditTrail @{
            Operation = "SystemStartup"
            Version = $script:JMLVersion
            ExecutingUser = $script:JMLExecutionContext.ExecutingUser
            ComputerName = $script:JMLExecutionContext.ComputerName
            SkipJiraIntegration = $SkipJiraIntegration.IsPresent
        }

        Write-Progress -Activity "Initializing JML System" -Status "Ready" -PercentComplete 100
        Start-Sleep -Milliseconds 500
        Write-Progress -Activity "Initializing JML System" -Completed

        # Main menu loop
        do {
            Show-MainMenu
            $choice = Read-Host

            switch ($choice) {
                "1" {
                    $ticketKey = Read-Host "Enter Jira ticket key (optional, press Enter to skip)"
                    if ([string]::IsNullOrWhiteSpace($ticketKey)) {
                        New-AdminAccount
                    } else {
                        New-AdminAccount -TicketKey $ticketKey.Trim().ToUpper()
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "2" {
                    $ticketKey = Read-Host "Enter Jira ticket key (optional, press Enter to skip)"
                    if ([string]::IsNullOrWhiteSpace($ticketKey)) {
                        Remove-StdAdminAccount
                    } else {
                        Remove-StdAdminAccount -TicketKey $ticketKey.Trim().ToUpper()
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "3" {
                    $ticketKey = Read-Host "Enter Jira ticket key (optional, press Enter to skip)"
                    if ([string]::IsNullOrWhiteSpace($ticketKey)) {
                        Reset-StdAdminAccount
                    } else {
                        Reset-StdAdminAccount -TicketKey $ticketKey.Trim().ToUpper()
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "4" {
                    Show-SystemInformation
                }
                "5" {
                    Clear-Host
                    Write-Host ""
                    Write-Host "Running JML Setup..." -ForegroundColor Cyan
                    Write-Host ""
                    $setupResult = Start-JMLSetup -ValidateEnvironment -SetupCredentials -InstallMissingModules
                    Write-Host ""
                    if ($setupResult) {
                        Write-Host "Setup completed successfully!" -ForegroundColor Green
                    } else {
                        Write-Host "Setup completed with issues. Please review the messages above." -ForegroundColor Yellow
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "6" {
                    Write-Host ""
                    Write-Host "Thank you for using the JML Admin Account Management System!" -ForegroundColor Green
                    Write-Host "Version $script:JMLVersion - Enhanced Security Edition" -ForegroundColor Cyan
                    Write-Host ""

                    Write-SecureLog -Message "JML System shutdown by user" -LogLevel "INFO" -AuditTrail @{
                        Operation = "SystemShutdown"
                        Duration = ((Get-Date) - $script:JMLExecutionContext.StartTime).TotalSeconds
                    }

                    return
                }
                default {
                    Write-Host ""
                    Write-Host "Invalid choice. Please select a number between 1 and 6." -ForegroundColor Red
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
            }
        } while ($true)
    }
    catch {
        Write-Error "Main execution failed: $($_.Exception.Message)"
        Write-SecureLog -Message "Main execution error: $($_.Exception.Message)" -LogLevel "ERROR"
        exit 1
    }
}

#endregion

#region Script Execution

# Start main execution
try {
    Start-MainExecution
}
catch {
    Write-Error "Script execution failed: $($_.Exception.Message)"
    exit 1
}
finally {
    # Cleanup
    if ($script:CurrentLogPath) {
        Write-Host "Log file saved: $script:CurrentLogPath" -ForegroundColor Gray
    }
}

#endregion
