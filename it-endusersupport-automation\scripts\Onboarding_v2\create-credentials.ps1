# Create encrypted credentials file for JML testing
$secureCreds = @{}

# Add Jira credentials
$username = "<EMAIL>"
$password = ConvertTo-SecureString "test-api-token-123" -AsPlainText -Force

$secureCreds["AdminScript-JiraUsername"] = $username
$secureCreds["AdminScript-JiraApiToken"] = $password

# Export to encrypted XML file
$secureCreds | Export-Clixml -Path ".\Modules\SecureCredentials.xml" -Force

Write-Host "Credentials file created successfully at: .\Modules\SecureCredentials.xml" -ForegroundColor Green
Write-Host "Username: $username" -ForegroundColor Yellow
Write-Host "Password: [ENCRYPTED]" -ForegroundColor Yellow
